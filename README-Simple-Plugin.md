# 节点媒体内容插件化架构 - 简化版

一个简单易用的`INodeMediaContent`实现类管理方案，支持SPI自动发现和Spring手动注册。

## 🎯 核心特性

- **SPI自动发现**: 配置文件自动注册
- **Spring手动注册**: 运行时覆盖SPI配置  
- **智能反序列化**: 根据JSON自动选择实现类
- **节点更新机制**: 自动检测已存在节点并更新内容
- **节点删除机制**: 支持DELETE事件删除指定节点
- **简单易用**: 最少配置，直接使用

## 🚀 快速上手

### 1. 创建Provider

```java
public class YourBusinessProvider implements NodeMediaContentProvider {
    @Override
    public String getBusinessType() {
        return "YOUR_BUSINESS";
    }
    
    @Override
    public Map<Integer, Class<? extends INodeMediaContent>> getImplementations() {
        Map<Integer, Class<? extends INodeMediaContent>> map = new HashMap<>();
        map.put(3001, YourTextContent.class);
        map.put(3002, YourCardContent.class);
        return map;
    }
}
```

### 2. 配置SPI

创建文件：`src/main/resources/META-INF/services/com.cockatiel.ai.ext.def.sdk.NodeMediaContentProvider`
```
com.your.business.provider.YourBusinessProvider
```

### 3. JSON格式

```json
{
  "businessType": "YOUR_BUSINESS",
  "mediaType": 3001,
  "content": "your data"
}
```

### 4. 使用

```java
// 自动反序列化
EventData eventData = objectMapper.readValue(json, EventData.class);

// 手动查找
@Autowired
private NodeMediaContentManager manager;
Class<? extends INodeMediaContent> clazz = manager.findImplementation("YOUR_BUSINESS", 3001);
```

## 📋 媒体类型分配

| 业务类型 | 媒体类型范围 |
|---------|-------------|
| DZ_HAMMER | 1001-1999 |
| YOUR_BUSINESS | 3001-3999 |
| OTHER_BUSINESS | 4001-4999 |

## 🔄 节点更新机制

系统支持智能节点更新：

### 工作原理
- **nodeId作为唯一键**: 每个节点通过nodeId唯一标识
- **自动检测**: 保存时自动检查节点是否已存在
- **智能处理**: 
  - 节点不存在 → 创建新节点
  - 节点已存在 → 更新节点内容

### 使用场景
```java
// 第一次保存 - 创建新节点
{
  "nodeId": 12345,
  "content": "初始内容",
  "mediaType": 1002
}

// 再次保存相同nodeId - 更新内容
{
  "nodeId": 12345,
  "content": "更新后的内容", 
  "mediaType": 1002
}
```

### 日志输出
```
// 新节点
保存MessageNode成功: nodeId=12345, messageId=67890

// 更新节点  
更新MessageNode成功: nodeId=12345, messageId=67890
```

## 🗑️ 节点删除机制

系统支持通过DELETE事件删除节点：

### 工作原理
- **事件类型检测**: 检查`eventType`是否为`DELETE_EVENT`(2004)
- **节点存在性检查**: 删除前检查节点是否存在
- **安全删除**: 只删除存在的节点，不存在时记录警告

### 使用场景
```java
// DELETE事件格式
{
  "eventType": 2004,  // DELETE_EVENT
  "eventData": {
    "nodeList": [
      {
        "nodeId": 12345,
        "mediaType": 1002
      }
    ]
  }
}
```

### 处理流程
```
接收事件 → 检查eventType
    ↓
eventType=2004? → 是：进入删除流程
    ↓                ↓
   否：正常保存/更新   解析nodeList → 遍历每个node
                        ↓
                    检查nodeId是否存在
                        ↓
                    存在？ → 是：删除节点 → 记录"删除成功"
                        ↓
                       否：记录"节点不存在"警告
```

### 日志输出
```
// 删除成功
删除MessageNode成功: nodeId=12345, eventId=100

// 节点不存在
DELETE事件中的节点不存在: nodeId=12345, eventId=100
```

## 🔧 可选Spring注册

```java
@Configuration
public class YourBusinessConfig {
    @Autowired
    private ApplicationContext context;
    
    @PostConstruct
    public void register() {
        try {
            Object manager = context.getBean("nodeMediaContentManager");
            manager.getClass().getMethod("registerImplementation", String.class, Integer.class, Class.class)
                .invoke(manager, "YOUR_BUSINESS", 3001, YourTextContent.class);
        } catch (Exception e) {
            // 忽略，使用SPI
        }
    }
}
```

## ❗ 注意事项

### 必需字段
- JSON必须包含`businessType`和`mediaType`
- 缺少`businessType`时默认使用`"DZ_HAMMER"`

### 优先级
- Spring注册 > SPI注册
- 后注册覆盖先注册

### 节点更新
- **nodeId必须唯一**: 相同nodeId会触发更新而非创建
- **内容完全替换**: 更新时会完全替换节点内容
- **保持其他字段**: messageId、mediaType、eventId等保持不变

### 节点删除
- **DELETE事件优先**: eventType=2004时优先进入删除流程
- **安全删除**: 只删除存在的节点，避免重复删除
- **批量删除**: 支持在一个事件中删除多个节点

### 错误处理
```java
try {
    EventData data = objectMapper.readValue(json, EventData.class);
} catch (IOException e) {
    if (e.getMessage().contains("未找到实现类")) {
        // 处理未注册类型
    } else if (e.getMessage().contains("缺少必要字段")) {
        // 处理字段缺失
    }
}
```

## 🔍 故障排查

### 常见问题
1. **"未找到实现类"** → 检查SPI配置文件和类名
2. **"缺少必要字段"** → 确保JSON包含businessType和mediaType
3. **"反序列化失败"** → 检查实现类字段匹配和无参构造函数
4. **"NodeMediaContentManager is null"** → 已通过静态注册表解决
5. **"节点重复创建"** → 检查nodeId是否正确设置
6. **"DELETE事件无效"** → 检查eventType是否为2004

### 调试
```yaml
logging:
  level:
    com.cockatiel.metaagent.infrastructure: DEBUG
```

### 架构说明
系统使用双重机制确保反序列化正常工作：
- **NodeMediaContentManager**: Spring管理的主要组件
- **NodeMediaContentRegistry**: 静态注册表，解决Jackson反序列化中的依赖注入问题

## 📁 项目结构

```
your-business-sdk/
├── src/main/java/com/your/business/
│   ├── provider/YourBusinessProvider.java
│   └── model/YourTextContent.java
├── src/main/resources/META-INF/services/
│   └── com.cockatiel.ai.ext.def.sdk.NodeMediaContentProvider
└── pom.xml
```

## 🎯 最佳实践

**✅ 推荐**
- 独立的媒体类型范围
- JSON明确指定字段
- 优先使用SPI注册
- 确保nodeId唯一性
- 删除前检查节点存在性

**❌ 避免**  
- 不同业务使用相同媒体类型
- 依赖默认businessType推断
- 重复使用相同nodeId创建不同节点
- 删除不存在的节点

---

**核心API**
- `manager.findImplementation(businessType, mediaType)` - 查找实现类
- `manager.registerImplementation(businessType, mediaType, class)` - 手动注册
- `manager.getAllBusinessTypes()` - 获取业务类型
- `repository.findByNodeId(nodeId)` - 查找已存在节点
- `repository.updateContent(nodeId, content)` - 更新节点内容
- `repository.deleteByNodeId(nodeId)` - 删除指定节点

简单、直接、可靠 - 适合大多数业务场景。 