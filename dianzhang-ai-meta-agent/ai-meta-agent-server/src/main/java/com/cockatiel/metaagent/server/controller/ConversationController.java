package com.cockatiel.metaagent.server.controller;

import com.cockatiel.metaagent.facadeimpl.service.DzBossConversationService;
import com.cockatiel.metaagent.infrastructure.model.entity.DzBossConversation;
import com.cockatiel.metaagent.server.model.req.ConversationListReq;
import com.cockatiel.metaagent.server.model.resp.ConversationResp;
import com.cockatiel.metaagent.server.model.vo.ConversationVO;
import com.google.common.collect.Lists;
import com.zhipin.cockatiel.common.log.annotation.BusinessLog;
import com.zhipin.cockatiel.common.utils.ApiResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yang<PERSON><PERSON>
 * @program ： ConversationController
 * @description :
 * @create :2025-05-27
 */
@Slf4j
@RestController
@RequestMapping("/api/dz/ai/conversation")
@BusinessLog(module = "url", type = "url", childrenType = "conversation")
public class ConversationController {

    @Resource
    private DzBossConversationService dzBossConversationService;

    @PostMapping("/list")
    public ApiResult<ConversationResp> listConversation(@RequestBody ConversationListReq req) {
        List<DzBossConversation> convList = dzBossConversationService.findConversationsByUserId(
                req.getBossId(), req.getPage(), req.getPageSize());
        ConversationResp resp = new ConversationResp();
        resp.setConversationList(convert2VOList(convList));
        resp.setHasMore(convList.size() >= req.getPageSize());
        return ApiResult.successResult(resp);
    }

    private List<ConversationVO> convert2VOList(List<DzBossConversation> conversationList) {
        if (CollectionUtils.isEmpty(conversationList)){
            return Lists.newArrayList();
        }
        return conversationList.stream().map(this::convert2VO).collect(Collectors.toList());
    }

    private ConversationVO convert2VO(DzBossConversation conversation) {
        ConversationVO conversationVO = new ConversationVO();
        conversationVO.setId(conversation.getId());
        conversationVO.setConversationId(conversation.getConversationId());
        conversationVO.setTitle(conversation.getTitle());
        conversationVO.setBossId(conversation.getBossId());
        conversationVO.setJobId(conversation.getJobId());
        conversationVO.setJobDesc("[沟通职位] 服务员·海底捞 (太阳宫店)");
        conversationVO.setCreateTime(conversation.getCreateTime().getTime());
        conversationVO.setUpdateTime(conversation.getUpdateTime().getTime());
        return conversationVO;
    }
}
