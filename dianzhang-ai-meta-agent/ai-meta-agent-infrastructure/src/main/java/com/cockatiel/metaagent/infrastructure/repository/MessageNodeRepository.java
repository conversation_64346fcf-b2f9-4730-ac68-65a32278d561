package com.cockatiel.metaagent.infrastructure.repository;

import com.cockatiel.metaagent.infrastructure.model.entity.MessageNode;

import java.util.List;

/**
 * 消息节点仓库接口
 * 
 * <AUTHOR>
 */
public interface MessageNodeRepository {

    /**
     * 保存消息节点
     * 
     * @param messageNode 消息节点实体
     * @return 保存后的消息节点实体
     */
    MessageNode save(MessageNode messageNode);

    /**
     * 根据节点ID更新节点内容
     * 
     * @param nodeId 节点ID
     * @param content 新内容
     * @return 是否更新成功
     */
    boolean updateContent(Long nodeId, String content);

    /**
     * 根据消息ID查找所有节点
     * 
     * @param messageId 消息ID
     * @return 消息节点列表（按eventId排序）
     */
    List<MessageNode> findByMessageId(Long messageId);

    /**
     * 根据节点ID查找节点
     * 
     * @param nodeId 节点ID
     * @return 消息节点实体，如果不存在返回null
     */
    MessageNode findByNodeId(Long nodeId);

    /**
     * 根据节点ID删除节点
     * 
     * @param nodeId 节点ID
     * @return 是否删除成功
     */
    boolean deleteByNodeId(Long nodeId);
} 