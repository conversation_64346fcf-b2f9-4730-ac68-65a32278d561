package com.cockatiel.metaagent.infrastructure.manager;

import com.cockatiel.ai.ext.def.sdk.INodeMediaContent;
import com.cockatiel.ai.ext.def.sdk.NodeMediaContentProvider;
import com.cockatiel.metaagent.infrastructure.util.NodeMediaContentRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 节点媒体内容管理器
 * 简化版：支持Spring注册和SPI发现
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class NodeMediaContentManager {

    /**
     * 类型映射缓存
     * key: businessType:mediaType, value: implementationClass
     */
    private final Map<String, Class<? extends INodeMediaContent>> typeMapping = new ConcurrentHashMap<>();

    @PostConstruct
    public void initialize() {
        // 初始化静态注册表
        NodeMediaContentRegistry.initialize();
        
        loadSpiProviders();
        log.info("NodeMediaContentManager初始化完成，共注册{}个类型", typeMapping.size());
    }

    /**
     * 加载SPI提供者
     */
    private void loadSpiProviders() {
        try {
            ServiceLoader<NodeMediaContentProvider> serviceLoader = ServiceLoader.load(NodeMediaContentProvider.class);
            for (NodeMediaContentProvider provider : serviceLoader) {
                String businessType = provider.getBusinessType();
                Map<Integer, Class<? extends INodeMediaContent>> implementations = provider.getImplementations();
                
                for (Map.Entry<Integer, Class<? extends INodeMediaContent>> entry : implementations.entrySet()) {
                    String key = businessType + ":" + entry.getKey();
                    typeMapping.put(key, entry.getValue());
                }
                
                log.info("加载SPI提供者: {} ({}个类型)", businessType, implementations.size());
            }
        } catch (Exception e) {
            log.warn("加载SPI提供者失败", e);
        }
    }

    /**
     * Spring方式注册实现类（会覆盖SPI注册的类型）
     */
    public void registerImplementation(String businessType, Integer mediaType, 
                                     Class<? extends INodeMediaContent> implementationClass) {
        String key = businessType + ":" + mediaType;
        typeMapping.put(key, implementationClass);
        
        // 同时更新静态注册表
        NodeMediaContentRegistry.registerImplementation(businessType, mediaType, implementationClass);
        
        log.info("Spring注册: {} -> {}", key, implementationClass.getSimpleName());
    }

    /**
     * 查找实现类
     */
    public Class<? extends INodeMediaContent> findImplementation(String businessType, Integer mediaType) {
        String key = businessType + ":" + mediaType;
        return typeMapping.get(key);
    }

    /**
     * 获取所有业务类型
     */
    public Set<String> getAllBusinessTypes() {
        Set<String> businessTypes = new HashSet<>();
        for (String key : typeMapping.keySet()) {
            businessTypes.add(key.split(":")[0]);
        }
        return businessTypes;
    }
} 