package com.cockatiel.metaagent.infrastructure.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * AI消息实体
 * 
 * <AUTHOR>
 */
@Data
public class AiMessage implements Serializable {

    /**
     * 消息ID
     */
    private Long messageId;

    /**
     * 会话ID
     */
    private String conversationId;

    /**
     * 消息角色：user-用户发送, ai-AI回复
     */
    private String role;

    /**
     * 消息标题（原sectionName，有值表示是section块状消息，无值表示单独消息）
     */
    private String title;

    /**
     * 折叠状态：1-展开, 2-折叠（null表示没有展开动作）
     */
    private Integer status;

    /**
     * 消息创建时间
     */
    private Date createTime;

    /**
     * 消息更新时间
     */
    private Date updateTime;
} 