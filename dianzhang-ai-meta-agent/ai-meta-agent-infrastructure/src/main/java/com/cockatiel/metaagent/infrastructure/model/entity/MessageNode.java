package com.cockatiel.metaagent.infrastructure.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息节点实体
 * 用于存储消息的具体内容节点
 * 
 * <AUTHOR>
 */
@Data
public class MessageNode implements Serializable {

    private Long id;

    /**
     * 节点ID
     */
    private Long nodeId;

    /**
     * 消息ID（关联AiMessage）
     */
    private Long messageId;

    /**
     * 媒体类型：1001-普通消息, 1002-卡片消息, 1003-图片消息等
     */
    private Integer mediaType;

    /**
     * 节点内容（JSON格式存储，根据mediaType解析不同结构）
     */
    private String content;

    /**
     * 事件ID（EventData中的eventId，作为排序依据）
     */
    private Integer eventId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

} 