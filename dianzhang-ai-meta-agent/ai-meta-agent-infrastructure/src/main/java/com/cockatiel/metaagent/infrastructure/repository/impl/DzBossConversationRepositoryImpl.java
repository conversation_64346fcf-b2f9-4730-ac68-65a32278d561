package com.cockatiel.metaagent.infrastructure.repository.impl;

import com.cockatiel.metaagent.infrastructure.dao.DzBossConversationDAO;
import com.cockatiel.metaagent.infrastructure.model.entity.DzBossConversation;
import com.cockatiel.metaagent.infrastructure.repository.DzBossConversationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * AI会话仓库实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Repository
public class DzBossConversationRepositoryImpl implements DzBossConversationRepository {

    @Resource
    private DzBossConversationDAO dzBossConversationDAO;

    @Override
    public DzBossConversation save(DzBossConversation conversation) {
        dzBossConversationDAO.insert(conversation);
        return conversation;
    }

    @Override
    public Optional<DzBossConversation> findByConversationId(String conversationId) {
        return Optional.ofNullable(null);
    }

    @Override
    public List<DzBossConversation> findByUserId(Long bossId, Integer page, Integer pageSize) {
        int offset = (page - 1) * pageSize;
        return dzBossConversationDAO.pageByBossId(bossId, offset, pageSize);
    }

    @Override
    public boolean updateLastMessage(String conversationId, String lastMessage, LocalDateTime lastMessageTime, Integer messageCount) {
        return true;
    }

    @Override
    public boolean updateTitle(String conversationId, String title) {
        dzBossConversationDAO.updateTitle(conversationId, title);
        return true;
    }
} 