package com.cockatiel.metaagent.infrastructure.repository;

import com.cockatiel.metaagent.infrastructure.model.entity.DzBossConversation;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * AI会话仓库接口
 * 
 * <AUTHOR>
 */
public interface DzBossConversationRepository {

    /**
     * 保存会话
     * 
     * @param conversation 会话实体
     * @return 保存后的会话实体
     */
    DzBossConversation save(DzBossConversation conversation);

    /**
     * 根据会话ID查找会话
     * 
     * @param conversationId 会话ID
     * @return 会话实体
     */
    Optional<DzBossConversation> findByConversationId(String conversationId);

    /**
     * 根据用户ID查找会话列表
     * 
     * @param bossId 用户ID
     * @param page 页码
     * @param pageSize 页数
     * @return 会话列表（按更新时间倒序）
     */
    List<DzBossConversation> findByUserId(Long bossId, Integer page, Integer pageSize);

    /**
     * 更新会话的最后消息信息
     * 
     * @param conversationId 会话ID
     * @param lastMessage 最后消息内容
     * @param lastMessageTime 最后消息时间
     * @param messageCount 消息总数
     * @return 是否更新成功
     */
    boolean updateLastMessage(String conversationId, String lastMessage, LocalDateTime lastMessageTime, Integer messageCount);

    /**
     * 更新会话标题
     * 
     * @param conversationId 会话ID
     * @param title 新标题
     * @return 是否更新成功
     */
    boolean updateTitle(String conversationId, String title);
} 