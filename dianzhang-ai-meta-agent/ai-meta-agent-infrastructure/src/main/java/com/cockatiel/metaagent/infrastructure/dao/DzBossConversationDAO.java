package com.cockatiel.metaagent.infrastructure.dao;

import cn.techwolf.jade.annotation.DAO;
import cn.techwolf.jade.annotation.SQL;
import com.cockatiel.metaagent.infrastructure.model.entity.DzBossConversation;

import java.util.List;

/**
 * <AUTHOR> ya<PERSON><PERSON><PERSON>
 * @program ： ConversationDAO
 * @description :
 * @create :2025-05-27
 */
@DAO(catalog = "blue_ai_agent")
public interface DzBossConversationDAO {

    String TABLE_NAME = "dz_boss_conversation";

    String INSERT_COLUMNS = " conversation_id, boss_id, job_id, title ";

    String ALL_COLUMNS = "id, create_time, update_time, " + INSERT_COLUMNS;

    @SQL("insert into " + TABLE_NAME + " (" + INSERT_COLUMNS + ") values (:1.conversationId, :1.bossId, :1.jobId, :1.title) ")
    void insert(DzBossConversation dzBossConversation);

    @SQL("update " + TABLE_NAME + " set title = :2 where conversation_id = :1 ")
    void updateTitle(String conversationId, String title);

    @SQL("select " + ALL_COLUMNS + " from " + TABLE_NAME + " where boss_id = :1 and status = 0 order by update_time desc limit :2, :3 ")
    List<DzBossConversation> pageByBossId(Long bossId, Integer offset, Integer limit);

}
