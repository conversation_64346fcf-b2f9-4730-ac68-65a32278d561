package com.cockatiel.metaagent.infrastructure.deserializer;

import com.cockatiel.ai.ext.def.sdk.INodeMediaContent;
import com.cockatiel.metaagent.infrastructure.util.NodeMediaContentRegistry;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * 简化版节点媒体内容反序列化器
 * 使用静态注册表，避免依赖注入问题
 * 
 * <AUTHOR>
 */
@Slf4j
public class SmartNodeMediaContentDeserializer extends JsonDeserializer<INodeMediaContent> {

    @Override
    public INodeMediaContent deserialize(JsonParser p, DeserializationContext ctxt) 
            throws IOException, JsonProcessingException {
        
        ObjectMapper mapper = (ObjectMapper) p.getCodec();
        JsonNode node = mapper.readTree(p);
        
        // 获取业务类型和媒体类型
        String businessType = getBusinessType(node);
        Integer mediaType = getMediaType(node);
        
        if (businessType == null || mediaType == null) {
            throw new IOException("缺少必要字段: businessType=" + businessType + ", mediaType=" + mediaType);
        }
        
        // 查找实现类
        Class<? extends INodeMediaContent> implementationClass = 
            NodeMediaContentRegistry.findImplementation(businessType, mediaType);
        
        if (implementationClass == null) {
            throw new IOException("未找到实现类: businessType=" + businessType + ", mediaType=" + mediaType);
        }
        
        try {
            // 创建一个新的JSON节点，移除不属于目标类的字段
            ObjectNode cleanNode = createCleanNode(node);
            
            return mapper.treeToValue(cleanNode, implementationClass);
        } catch (Exception e) {
            throw new IOException("反序列化失败: " + implementationClass.getSimpleName() + ", 原因: " + e.getMessage(), e);
        }
    }

    /**
     * 创建清理后的JSON节点，移除mediaType和businessType字段
     */
    private ObjectNode createCleanNode(JsonNode originalNode) {
        ObjectNode cleanNode = originalNode.deepCopy();
        
        // 移除反序列化时不需要的字段
        cleanNode.remove("mediaType");
        cleanNode.remove("businessType");
        
        return cleanNode;
    }

    /**
     * 获取业务类型
     */
    private String getBusinessType(JsonNode node) {
        JsonNode businessTypeNode = node.get("businessType");
        if (businessTypeNode != null && !businessTypeNode.isNull()) {
            return businessTypeNode.asText();
        }
        
        // 默认使用DZ_HAMMER
        return "DZ_HAMMER";
    }

    /**
     * 获取媒体类型
     */
    private Integer getMediaType(JsonNode node) {
        JsonNode mediaTypeNode = node.get("mediaType");
        if (mediaTypeNode != null && !mediaTypeNode.isNull()) {
            return mediaTypeNode.asInt();
        }
        return null;
    }
} 