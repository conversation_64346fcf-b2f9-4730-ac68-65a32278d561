package com.cockatiel.metaagent.infrastructure.repository.impl;

import com.cockatiel.metaagent.infrastructure.dao.MessageNodeDAO;
import com.cockatiel.metaagent.infrastructure.model.entity.MessageNode;
import com.cockatiel.metaagent.infrastructure.repository.MessageNodeRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 消息节点仓库数据库实现类
 * 使用数据库持久化存储
 * 
 * <AUTHOR>
 */
@Slf4j
@Repository
public class MessageNodeRepositoryImpl implements MessageNodeRepository {

    @Resource
    private MessageNodeDAO messageNodeDAO;

    @Override
    public MessageNode save(MessageNode messageNode) {
        messageNodeDAO.insert(messageNode);
        log.debug("成功保存消息节点到数据库，nodeId: {}, messageId: {}, mediaType: {}",
                messageNode.getNodeId(), messageNode.getMessageId(), messageNode.getMediaType());
        return messageNode;
    }

    @Override
    public boolean updateContent(Long nodeId, String content) {
        messageNodeDAO.updateContent(nodeId, content);
        log.debug("成功更新数据库节点内容");
        return true;
    }

    @Override
    public List<MessageNode> findByMessageId(Long messageId) {
        log.debug("从数据库查找消息的所有节点，messageId: {}", messageId);
        return messageNodeDAO.findByMessageId(messageId);
    }

    @Override
    public MessageNode findByNodeId(Long nodeId) {
        log.debug("从数据库查找节点，nodeId: {}", nodeId);
        return messageNodeDAO.findByNodeId(nodeId);
    }

    @Override
    public boolean deleteByNodeId(Long nodeId) {
        messageNodeDAO.deleteByNodeId(nodeId);
        log.debug("成功删除数据库节点，nodeId: {}", nodeId);
        return true;
    }
} 