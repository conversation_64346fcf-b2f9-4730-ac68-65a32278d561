package com.cockatiel.metaagent.infrastructure.util;

import com.cockatiel.ai.ext.def.sdk.INodeMediaContent;
import com.cockatiel.ai.ext.def.sdk.NodeMediaContentProvider;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.ServiceLoader;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 节点媒体内容静态注册表
 * 解决反序列化器中依赖注入问题
 * 
 * <AUTHOR>
 */
@Slf4j
public class NodeMediaContentRegistry {

    /**
     * 静态类型映射
     */
    private static final Map<String, Class<? extends INodeMediaContent>> TYPE_MAPPING = new ConcurrentHashMap<>();

    /**
     * 初始化标志
     */
    private static volatile boolean initialized = false;

    /**
     * 初始化注册表
     */
    public static void initialize() {
        if (initialized) {
            return;
        }
        
        synchronized (NodeMediaContentRegistry.class) {
            if (initialized) {
                return;
            }
            
            loadSpiProviders();
            initialized = true;
            log.info("NodeMediaContentRegistry初始化完成，共注册{}个类型", TYPE_MAPPING.size());
        }
    }

    /**
     * 加载SPI提供者
     */
    private static void loadSpiProviders() {
        try {
            ServiceLoader<NodeMediaContentProvider> serviceLoader = ServiceLoader.load(NodeMediaContentProvider.class);
            for (NodeMediaContentProvider provider : serviceLoader) {
                String businessType = provider.getBusinessType();
                Map<Integer, Class<? extends INodeMediaContent>> implementations = provider.getImplementations();
                
                for (Map.Entry<Integer, Class<? extends INodeMediaContent>> entry : implementations.entrySet()) {
                    String key = businessType + ":" + entry.getKey();
                    TYPE_MAPPING.put(key, entry.getValue());
                }
                
                log.info("加载SPI提供者: {} ({}个类型)", businessType, implementations.size());
            }
        } catch (Exception e) {
            log.warn("加载SPI提供者失败", e);
        }
    }

    /**
     * 注册实现类
     */
    public static void registerImplementation(String businessType, Integer mediaType, 
                                            Class<? extends INodeMediaContent> implementationClass) {
        initialize();
        String key = businessType + ":" + mediaType;
        TYPE_MAPPING.put(key, implementationClass);
        log.info("注册实现类: {} -> {}", key, implementationClass.getSimpleName());
    }

    /**
     * 查找实现类
     */
    public static Class<? extends INodeMediaContent> findImplementation(String businessType, Integer mediaType) {
        initialize();
        String key = businessType + ":" + mediaType;
        return TYPE_MAPPING.get(key);
    }
} 