package com.cockatiel.metaagent.infrastructure.model.message;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 消息DTO
 * 对应前端期望的IMessage接口结构
 * 
 * <AUTHOR>
 */
@Data
public class MessageDTO implements Serializable {

    /**
     * 消息ID
     */
    private Long messageId;

    /**
     * 消息角色：user-用户发送, ai-AI回复
     */
    private String role;

    /**
     * 消息标题（原sectionName，有值表示是section块状消息，无值表示单独消息）
     */
    private String title;

    /**
     * 折叠状态：1-展开, 2-折叠（null表示没有展开动作）
     */
    private Integer status;

    /**
     * 节点列表
     */
    private List<MessageNodeDTO> nodeList;

    /**
     * 消息节点DTO
     */
    @Data
    public static class MessageNodeDTO implements Serializable {

        /**
         * 节点ID
         */
        private Long nodeId;

        /**
         * 媒体类型：1001-普通消息, 1002-卡片消息等
         */
        private Integer mediaType;

        /**
         * 节点内容（JSON字符串，根据mediaType解析不同结构）
         */
        private String content;
    }
} 