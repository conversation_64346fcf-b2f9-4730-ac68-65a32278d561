package com.cockatiel.metaagent.infrastructure.repository.impl;

import cn.techwolf.cache.service.ICacheService;
import com.cockatiel.ai.ext.def.sdk.INodeMediaContent;
import com.cockatiel.metaagent.common.enums.SseEventTypeEnum;
import com.cockatiel.metaagent.infrastructure.client.IDGenApiClient;
import com.cockatiel.metaagent.infrastructure.model.entity.MessageNode;
import com.cockatiel.metaagent.infrastructure.model.entity.AiMessage;
import com.cockatiel.metaagent.infrastructure.model.sse.EventData;
import com.cockatiel.metaagent.infrastructure.model.sse.SseEvent;
import com.cockatiel.metaagent.infrastructure.repository.EventHistoryRepository;
import com.cockatiel.metaagent.infrastructure.repository.MessageNodeRepository;
import com.cockatiel.metaagent.infrastructure.repository.AiMessageRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;

import static com.cockatiel.metaagent.common.enums.CacheKeyEnum.EVENT_COUNTER_PREFIX;
import static com.cockatiel.metaagent.common.enums.CacheKeyEnum.EVENT_HISTORY_PREFIX;

/**
 * 事件历史仓库实现类
 * 支持消息存储逻辑
 */
@Slf4j
@Repository
public class EventHistoryRepositoryImpl implements EventHistoryRepository {

    @Resource
    private ICacheService cacheService;
    @Resource
    private MessageNodeRepository messageNodeRepository;
    @Resource
    @Qualifier("aiMessageRepositoryDatabase")
    private AiMessageRepository aiMessageRepository;
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void addEvent(String conversationId, ServerSentEvent<SseEvent> event) {
        try {
            // 只保存事件数据，不保存整个ServerSentEvent对象
            String eventJson = objectMapper.writeValueAsString(event.data());
            
            // 存储临时事件，做断点续传
            String eventKey = EVENT_HISTORY_PREFIX + conversationId + ":" + event.id();
            cacheService.set(eventKey, eventJson, EVENT_HISTORY_PREFIX.getExpire());

            // 存储messageNode，做历史消息同步
            saveMessageNodes(conversationId, event.data());
            
            log.info("保存事件到Redis: conversationId={}, eventId={}", conversationId, event.id());
        } catch (JsonProcessingException e) {
            log.error("序列化事件失败: conversationId={}, eventId={}", conversationId, event.id(), e);
        }
    }

    /**
     * 保存MessageNode节点
     */
    private void saveMessageNodes(String conversationId, SseEvent sseEvent) {
        try {
            if (sseEvent == null || StringUtils.isBlank(sseEvent.getEventData())) {
                log.debug("EventData为空，跳过MessageNode保存");
                return;
            }
            
            // 检查是否为删除事件
            if (Objects.equals(SseEventTypeEnum.DELETE_EVENT.getCode(), sseEvent.getEventType())) {
                handleDeleteEvent(conversationId, sseEvent);
                return;
            }
            
            // 解析EventData，现在使用自定义反序列化器处理INodeMediaContent
            EventData eventData = objectMapper.readValue(sseEvent.getEventData(), EventData.class);
            
            // 检查是否有nodeList和messageId
            if (CollectionUtils.isEmpty(eventData.getNodeList())) {
                log.debug("NodeList为空，跳过MessageNode保存");
                return;
            }

            // 1. 处理消息存储逻辑
            handleMessageStorage(conversationId, eventData);
            
            // 2. 处理每个node
            for (INodeMediaContent nodeContent : eventData.getNodeList()) {
                try {
                    Long nodeId = nodeContent.getNodeId();
                    String content = nodeContent.serialize();
                    
                    // 检查节点是否已存在
                    MessageNode existingNode = messageNodeRepository.findByNodeId(nodeId);
                    
                    if (existingNode != null) {
                        // 节点已存在，更新内容
                        messageNodeRepository.updateContent(nodeId, content);
                        log.info("更新MessageNode成功: conversationId={}, nodeId={}, messageId={}, mediaType={}, eventId={}", 
                                conversationId, nodeId, eventData.getMessageId(), 
                                nodeContent.getMediaType(), sseEvent.getEventId());
                    } else {
                        // 节点不存在，创建新节点
                        MessageNode messageNode = new MessageNode();
                        messageNode.setNodeId(nodeId);
                        messageNode.setMessageId(eventData.getMessageId());
                        messageNode.setMediaType(nodeContent.getMediaType());
                        messageNode.setContent(content);
                        messageNode.setEventId(sseEvent.getEventId());
                        Date now = new Date();
                        messageNode.setCreateTime(now);
                        messageNode.setUpdateTime(now);
                        
                        // 保存到数据库
                        messageNodeRepository.save(messageNode);
                        log.info("保存MessageNode成功: conversationId={}, nodeId={}, messageId={}, mediaType={}, eventId={}", 
                                conversationId, messageNode.getNodeId(), messageNode.getMessageId(), 
                                messageNode.getMediaType(), messageNode.getEventId());
                    }
                    
                } catch (Exception e) {
                    log.error("保存/更新MessageNode失败: conversationId={}, nodeContentType={}, eventId={}", 
                            conversationId, nodeContent.getClass().getSimpleName(), sseEvent.getEventId(), e);
                }
            }
            
        } catch (Exception e) {
            log.error("解析EventData或保存MessageNode失败: conversationId={}, eventData={}", 
                    conversationId, sseEvent.getEventData(), e);
        }
    }

    /**
     * 处理消息存储逻辑
     * 1. 当message表中没有数据时，存储message
     * 2. 当status、title没有变化时，不执行更新
     * 3. 当status、title发生变化时，更新status、title
     */
    private void handleMessageStorage(String conversationId, EventData eventData) {
        Long messageId = eventData.getMessageId();
        String title = eventData.getTitle();
        Integer status = eventData.getStatus();

        // 构建或获取 AiMessage 对象
        AiMessage messageToSave = aiMessageRepository.findByMessageId(messageId);
        if (messageToSave == null) {
            messageToSave = new AiMessage();
            messageToSave.setMessageId(messageId);
            messageToSave.setConversationId(conversationId);
            messageToSave.setRole("ai");
            Date now = new Date();
            messageToSave.setCreateTime(now);}

        // 设置或更新需要变化的属性
        messageToSave.setTitle(title);
        messageToSave.setStatus(status);

        // 调用 save 方法，它会处理插入或更新的逻辑
        aiMessageRepository.save(messageToSave);

        log.info("处理消息存储: conversationId={}, messageId={}, title={}, status={}",
                conversationId, messageId, title, status);
    }

    /**
     * 处理删除事件
     */
    private void handleDeleteEvent(String conversationId, SseEvent sseEvent) {
        try {
            // 解析EventData
            EventData eventData = objectMapper.readValue(sseEvent.getEventData(), EventData.class);

            // 检查是否有nodeList
            if (CollectionUtils.isEmpty(eventData.getNodeList())) {
                log.debug("DELETE事件NodeList为空，跳过节点删除");
                return;
            }

            // 删除每个node
            for (INodeMediaContent nodeContent : eventData.getNodeList()) {
                Long nodeId = nodeContent.getNodeId();

                // 检查节点是否存在
                MessageNode existingNode = messageNodeRepository.findByNodeId(nodeId);

                if (existingNode != null) {
                    // 节点存在，删除节点
                    messageNodeRepository.deleteByNodeId(nodeId);
                    log.info("删除MessageNode成功: conversationId={}, nodeId={}, eventId={}",
                            conversationId, nodeId, sseEvent.getEventId());
                }
            }

        } catch (Exception e) {
            log.error("处理DELETE事件失败: conversationId={}, eventData={}",
                    conversationId, sseEvent.getEventData(), e);
        }
    }

    @Override
    public Queue<ServerSentEvent<SseEvent>> getEventsAfter(String conversationId, String uuid, Integer lastId) {
        Queue<ServerSentEvent<SseEvent>> result = new ConcurrentLinkedQueue<>();
        
        if (StringUtils.isBlank(conversationId)) {
            return result;
        }
        
        // 获取当前计数器的值
        int currentId = getOrCreateCounter(conversationId, uuid).get();
        if (currentId <= 0) {
            return result;
        }
        
        // 获取lastId之后的所有事件
        for (int i = lastId + 1; i <= currentId; i++) {
            String eventKey = EVENT_HISTORY_PREFIX + conversationId + ":" + uuid + ":" + i;
            String eventJson = cacheService.get(eventKey);
            
            if (StringUtils.isNotBlank(eventJson)) {
                try {
                    // 反序列化事件
                    SseEvent event = objectMapper.readValue(eventJson, SseEvent.class);
                    SseEventTypeEnum sseEventTypeEnum = SseEventTypeEnum.getByCode(event.getEventType());

                    // 构建ServerSentEvent对象
                    ServerSentEvent<SseEvent> serverSentEvent = ServerSentEvent.<SseEvent>builder()
                            .id(uuid + ":" + i)
                            .event(sseEventTypeEnum.getName())
                            .data(event)
                            .build();
                    
                    result.add(serverSentEvent);
                } catch (IOException e) {
                    log.error("反序列化事件失败: {}", eventKey, e);
                }
            }
        }
        return result;
    }

    @Override
    public Integer getNextEventId(String conversationId, String uuid) {
        if (StringUtils.isBlank(conversationId)) {
            return 0;
        }
        
        // 每次都从Redis递增计数器，确保分布式环境下的一致性
        String counterKey = EVENT_COUNTER_PREFIX + conversationId + ":" + uuid;
        Long value = cacheService.increment(counterKey);
        
        // 设置过期时间，防止无限增长
        cacheService.expire(counterKey, EVENT_COUNTER_PREFIX.getExpire());
        
        return value.intValue();
    }

    @Override
    public AtomicInteger getOrCreateCounter(String conversationId, String uuid) {
        if (StringUtils.isBlank(conversationId)) {
            return new AtomicInteger(0);
        }
        
        // 从Redis获取当前值
        String counterKey = EVENT_COUNTER_PREFIX + conversationId + ":" + uuid;
        Long value = cacheService.getLong(counterKey);
        
        if (value == null) {
            // Redis没有，创建新计数器
            value = 0L;
            cacheService.setLong(counterKey, value, EVENT_COUNTER_PREFIX.getExpire());
        }
        
        return new AtomicInteger(value.intValue());
    }
} 