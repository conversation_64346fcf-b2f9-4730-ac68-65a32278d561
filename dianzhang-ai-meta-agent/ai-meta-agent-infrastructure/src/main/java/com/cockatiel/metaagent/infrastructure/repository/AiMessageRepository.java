package com.cockatiel.metaagent.infrastructure.repository;

import com.cockatiel.metaagent.infrastructure.model.entity.AiMessage;

import java.util.List;

/**
 * AI消息仓库接口
 * 
 * <AUTHOR>
 */
public interface AiMessageRepository {

    /**
     * 保存消息
     * 
     * @param message 消息实体
     * @return 保存后的消息实体
     */
    AiMessage save(AiMessage message);

    /**
     * 批量保存消息
     * 
     * @param messages 消息列表
     * @return 保存后的消息列表
     */
    List<AiMessage> saveAll(List<AiMessage> messages);

    /**
     * 根据消息ID查找消息
     * 
     * @param messageId 消息ID
     * @return 消息实体，如果不存在返回null
     */
    AiMessage findByMessageId(Long messageId);

    /**
     * 根据会话ID查找消息列表
     * 
     * @param conversationId 会话ID
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 消息列表（按创建时间排序）
     */
    List<AiMessage> findByConversationId(String conversationId, Integer limit, Integer offset);

    /**
     * 查找会话中的最后一条消息
     * 
     * @param conversationId 会话ID
     * @return 最后一条消息，如果不存在返回null
     */
    AiMessage findLastMessageByConversationId(String conversationId);

    /**
     * 删除消息（软删除）
     * 
     * @param messageId 消息ID
     * @return 是否删除成功
     */
    boolean deleteByMessageId(Long messageId);

    /**
     * 删除会话中的所有消息
     * 
     * @param conversationId 会话ID
     * @return 删除的消息数量
     */
    int deleteByConversationId(String conversationId);

    /**
     * 统计会话中的消息数量
     * 
     * @param conversationId 会话ID
     * @return 消息数量
     */
    Long countByConversationId(String conversationId);

    /**
     * 根据会话ID查找消息ID列表
     * 
     * @param conversationId 会话ID
     * @return 消息ID列表
     */
    List<Long> findMessageIdsByConversationId(String conversationId);
} 