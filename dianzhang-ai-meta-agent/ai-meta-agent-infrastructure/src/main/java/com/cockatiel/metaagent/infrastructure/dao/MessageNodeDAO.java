package com.cockatiel.metaagent.infrastructure.dao;

import cn.techwolf.jade.annotation.DAO;
import cn.techwolf.jade.annotation.SQL;
import com.cockatiel.metaagent.infrastructure.model.entity.MessageNode;

import java.util.List;

/**
 * MessageNode数据访问层
 * 
 * <AUTHOR>
 */
@DAO(catalog = "blue_ai_agent")
public interface MessageNodeDAO {

    String TABLE_NAME = "dz_message_node";

    String INSERT_COLUMNS = " node_id, message_id, media_type, content, event_id ";

    String ALL_COLUMNS = "id, create_time, update_time, " + INSERT_COLUMNS;

    @SQL("insert into " + TABLE_NAME + " (" + INSERT_COLUMNS + ") values (:1.nodeId, :1.messageId, :1.mediaType, :1.content, :1.eventId) ")
    void insert(MessageNode messageNode);

    @SQL("update " + TABLE_NAME + " set content = :2 where node_id = :1 ")
    void updateContent(Long nodeId, String content);

    @SQL("select " + ALL_COLUMNS + " from " + TABLE_NAME + " where message_id = :1 order by event_id asc ")
    List<MessageNode> findByMessageId(Long messageId);

    @SQL("select " + ALL_COLUMNS + " from " + TABLE_NAME + " where node_id = :1 ")
    MessageNode findByNodeId(Long nodeId);

    @SQL("delete from " + TABLE_NAME + " where node_id = :1 ")
    void deleteByNodeId(Long nodeId);
} 