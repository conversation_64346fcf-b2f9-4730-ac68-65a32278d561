package com.cockatiel.metaagent.infrastructure.model.entity;

import com.google.gson.annotations.Expose;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * AI会话实体
 * 
 * <AUTHOR>
 */
@Data
public class DzBossConversation implements Serializable {

    @Expose
    private Long id;

    /**
     * 会话ID
     */
    @Expose
    private String conversationId;

    /**
     * 用户ID
     */
    @Expose
    private Long bossId;

    /**
     * 职位ID
     */
    @Expose
    private Long jobId;

    /**
     * 会话标题
     */
    @Expose
    private String title;

    /**
     * 会话状态：0-正常, 1-已删除
     */
    @Expose
    private Integer status;

    /**
     * 会话创建时间
     */
    @Expose
    private Date createTime;

    /**
     * 会话更新时间
     */
    @Expose
    private Date updateTime;

} 