package com.cockatiel.metaagent.facadeimpl.service.impl;

import com.cockatiel.metaagent.facadeimpl.service.DzBossConversationService;
import com.cockatiel.metaagent.facadeimpl.service.MessageAssemblyService;
import com.cockatiel.metaagent.infrastructure.model.entity.DzBossConversation;
import com.cockatiel.metaagent.infrastructure.repository.AiMessageRepository;
import com.cockatiel.metaagent.infrastructure.repository.DzBossConversationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 会话服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class DzBossConversationServiceImpl implements DzBossConversationService {

    @Resource
    private DzBossConversationRepository conversationRepository;
    @Resource
    private AiMessageRepository messageRepository;
    @Resource
    private MessageAssemblyService messageAssemblyService;

    @Override
    public DzBossConversation createConversation(String conversationId, Long bossId, Long jobId, String title) {
        DzBossConversation conversation = new DzBossConversation();
        conversation.setConversationId(conversationId);
        conversation.setBossId(bossId);
        conversation.setJobId(jobId);
        conversation.setTitle(StringUtils.hasText(title) ? title : "新对话");
        Date now = new Date();
        conversation.setCreateTime(now);
        conversation.setUpdateTime(now);
        return conversationRepository.save(conversation);
    }

    @Override
    public Optional<DzBossConversation> findConversationById(String conversationId) {
        return conversationRepository.findByConversationId(conversationId);
    }

    @Override
    public List<DzBossConversation> findConversationsByUserId(Long bossId, Integer page, Integer pageSize) {
        return conversationRepository.findByUserId(bossId, page, pageSize);
    }

    @Override
    public boolean updateConversationTitle(String conversationId, String title) {
        return conversationRepository.updateTitle(conversationId, title);
    }
} 