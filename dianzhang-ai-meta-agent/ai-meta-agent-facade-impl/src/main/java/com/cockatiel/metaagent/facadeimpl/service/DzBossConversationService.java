package com.cockatiel.metaagent.facadeimpl.service;

import com.cockatiel.metaagent.infrastructure.model.entity.DzBossConversation;

import java.util.List;
import java.util.Optional;

/**
 * 会话服务接口
 * 
 * <AUTHOR>
 */
public interface DzBossConversationService {

    /**
     * 创建新会话
     *
     * @param conversationId 会话ID
     * @param bossId 用户ID
     * @param jobId 职位ID
     * @param title 会话标题
     * @return 创建的会话
     */
    DzBossConversation createConversation(String conversationId, Long bossId, Long jobId, String title);

    /**
     * 根据会话ID查找会话
     *
     * @param conversationId 会话ID
     * @return 会话信息
     */
    Optional<DzBossConversation> findConversationById(String conversationId);

    /**
     * 查询用户的会话列表
     * 
     * @param bossId 用户ID
     * @param page 页码
     * @param pageSize 页数
     * @return 会话列表
     */
    List<DzBossConversation> findConversationsByUserId(Long bossId, Integer page, Integer pageSize);

    /**
     * 更新会话标题
     * 
     * @param conversationId 会话ID
     * @param title 新标题
     * @return 是否更新成功
     */
    boolean updateConversationTitle(String conversationId, String title);

} 