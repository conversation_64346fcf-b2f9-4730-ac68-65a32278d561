package com.cockatiel.ai.ext.sdk.hammer.provider;

import com.cockatiel.ai.ext.def.sdk.INodeMediaContent;
import com.cockatiel.ai.ext.def.sdk.NodeMediaContentProvider;
import com.cockatiel.ai.ext.sdk.hammer.common.DzMediaTypeEnum;
import com.cockatiel.ai.ext.sdk.hammer.model.media.SummaryCardNodeContent;
import com.cockatiel.ai.ext.sdk.hammer.model.media.TaskCardNodeContent;
import com.cockatiel.ai.ext.sdk.hammer.model.media.TextNodeContent;

import java.util.HashMap;
import java.util.Map;

/**
 * 店长Hammer业务的节点媒体内容提供者（简化版）
 * 
 * <AUTHOR>
 */
public class DzHammerNodeMediaContentProvider implements NodeMediaContentProvider {

    @Override
    public String getBusinessType() {
        return "DZ_HAMMER";
    }

    @Override
    public Map<Integer, Class<? extends INodeMediaContent>> getImplementations() {
        Map<Integer, Class<? extends INodeMediaContent>> implementations = new HashMap<>();
        implementations.put(DzMediaTypeEnum.TEXT.getType(), TextNodeContent.class);
        implementations.put(DzMediaTypeEnum.TASK_CARD.getType(), TaskCardNodeContent.class);
        implementations.put(DzMediaTypeEnum.SUMMARY_CARD.getType(), SummaryCardNodeContent.class);
        return implementations;
    }
} 