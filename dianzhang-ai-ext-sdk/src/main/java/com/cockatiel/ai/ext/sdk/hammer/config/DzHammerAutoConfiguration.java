package com.cockatiel.ai.ext.sdk.hammer.config;

import com.cockatiel.ai.ext.sdk.hammer.common.DzMediaTypeEnum;
import com.cockatiel.ai.ext.sdk.hammer.model.media.SummaryCardNodeContent;
import com.cockatiel.ai.ext.sdk.hammer.model.media.TaskCardNodeContent;
import com.cockatiel.ai.ext.sdk.hammer.model.media.TextNodeContent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * DZ_HAMMER业务SDK自动配置类（简化版）
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class DzHammerAutoConfiguration {

    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    public void registerImplementations() {
        try {
            // 尝试获取管理器并注册
            Object manager = applicationContext.getBean("nodeMediaContentManager");
            // 使用反射调用注册方法
            manager.getClass().getMethod("registerImplementation", String.class, Integer.class, Class.class)
                .invoke(manager, "DZ_HAMMER", DzMediaTypeEnum.TEXT.getType(), TextNodeContent.class);
            manager.getClass().getMethod("registerImplementation", String.class, Integer.class, Class.class)
                .invoke(manager, "DZ_HAMMER", DzMediaTypeEnum.TASK_CARD.getType(), TaskCardNodeContent.class);
            manager.getClass().getMethod("registerImplementation", String.class, Integer.class, Class.class)
                .invoke(manager, "DZ_HAMMER", DzMediaTypeEnum.SUMMARY_CARD.getType(), SummaryCardNodeContent.class);

            log.info("DZ_HAMMER业务SDK注册完成");
        } catch (Exception e) {
            log.debug("NodeMediaContentManager未找到，跳过Spring注册");
        }
    }
} 